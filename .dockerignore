# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
ENV/
env/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Development files
*.md
Makefile
.env*
*.example
requirements-dev.txt

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# SonarQube
sonar-project.properties
.sonarqube/

# Test files
tests/
test_*
*_test.py
