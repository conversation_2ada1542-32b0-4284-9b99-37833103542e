# Index processor module
# TODO: remove declarations and use engine module

import abc
import datetime
import json
import typing  # python 3.8<
from functools import reduce

import confluent_kafka as kafka
from cachetools import TTLCache

import db
import es

from ..logging_config import get_logger


DELETED_CACHE_SIZE = 10000
DELETED_CACHE_TTL = 60 * 60  # 1 hour


# Represents a message from Kafka
class KeepsMessage:
    topic: str
    id: str
    value: str
    payload: dict
    size: int
    source: kafka.Message
    op: str

    def __init__(self, msg: kafka.Message) -> None:
        self.source = msg
        self.size = len(msg)
        self.topic = msg.topic()

        key = msg.key()
        value = msg.value()

        self.id = key.decode("utf-8") if key else None
        self.value = value.decode("utf-8") if value else None
        self.payload = json.loads(self.value)["payload"] if value else None
        self.op = self.payload.get("op") if self.payload else None

    def __str__(self) -> str:
        return f'[{self.topic}] "{self.id}"="{self.value[:30] if self.value else None}"'

    def log(self, includePayload: bool = False) -> None:
        logger = get_logger("KeepsMessage")
        ts_type, ts_value = self.source.timestamp()

        logger.info("----------------------------------")
        logger.info(f"- timestamp: {datetime.datetime.fromtimestamp(ts_value / 1000).isoformat()} {ts_type}")
        logger.info(f"- topic: {self.topic} / partition: {self.source.partition()} / offset: {self.source.offset()}")
        logger.info(f"- id: {self.id}")
        logger.info(f"- size: {self.size}")
        logger.info(f"- value: {self.value[:50] + '...' if self.value else '--- empty ---'}")
        logger.info(f"- op: {self.op}")

        if not includePayload:
            return

        if self.payload:
            logger.info("vvv payload vvv")
            logger.info(json.dumps(self.payload, indent=2))
            logger.info("^^^ payload ^^^")
        else:
            logger.info("--- empty payload ---")

    # Returns the pure entity portion of the message, with optional path
    def entity(self, path: str = None, excludeBefore: bool = False):
        entity = self.payload and self.payload.get("after")
        if not entity and not excludeBefore:
            entity = self.payload and self.payload.get("before")

        if not entity:
            return None

        if not path:
            return entity

        keys = path.split(".")
        part = reduce(lambda e, key: e.get(key), keys, entity)
        return part


# Basic structure of a Processor
class AbstractProcessor(abc.ABC):
    def __init__(self):
        self.db = db
        self.es = es
        self._elastic_index = self.get_index_name()
        self._kafka_main_topic = self.get_kafka_main_topic()
        self._kafka_sub_topics = self.get_kafka_sub_topics()
        self._cache_deleted = TTLCache(maxsize=DELETED_CACHE_SIZE, ttl=DELETED_CACHE_TTL)
        self.logger = get_logger(self.__class__.__name__)

    @abc.abstractmethod
    def get_kafka_main_topic(self) -> str:
        pass

    @abc.abstractmethod
    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        pass

    def get_kafka_topics(self) -> list[str]:
        topics = []
        if self._kafka_main_topic:
            topics.append(self._kafka_main_topic)
        if self._kafka_sub_topics:
            topics.extend(self._kafka_sub_topics.keys())
        return topics

    @abc.abstractmethod
    def get_index_name(self) -> str:
        pass

    # @abc.abstractmethod
    def get_index_settings(self) -> str:
        return {
            "analysis": {
                "normalizer": {"case_insensitive": {"filter": ["lowercase"]}},
                "analyzer": {"folding": {"tokenizer": "standard", "filter": ["lowercase", "asciifolding"]}},
            }
        }

    @abc.abstractmethod
    def get_index_mappings(self) -> str:
        pass

    def get_deleted_ids(self) -> list[str]:
        return self._cache_deleted.keys()

    def remove_deleted_instances(self, deleted_ids: set[str], delete_msgs: list[KeepsMessage] = None):
        self.es.bulk_delete(self._elastic_index, deleted_ids)

    # Pre-processing for main document
    def pre_process_batch(self, batch: list[KeepsMessage]):
        delete_msgs = [msg for msg in batch if msg.topic == self._kafka_main_topic and msg.op == "d"]
        update_msgs = [msg for msg in batch if msg.topic != self._kafka_main_topic or msg.op != "d"]

        logical_deleted_msgs = [msg for msg in batch if msg.topic == self._kafka_main_topic and msg.op != "d" and msg.entity("deleted")]

        deleted_ids = self.extract_ids(delete_msgs)
        deleted_ids.update(self.extract_ids(logical_deleted_msgs))
        for id in deleted_ids:
            self._cache_deleted[id] = True

        if deleted_ids:
            self.log("-- Removing deleted docs...")
            all_deleted_msgs = (delete_msgs or []) + (logical_deleted_msgs or [])
            self.remove_deleted_instances(deleted_ids, delete_msgs=all_deleted_msgs)

        updated_ids = self.extract_ids(update_msgs, self._kafka_sub_topics)
        updated_ids.difference_update(deleted_ids)
        updated_ids.difference_update(self._cache_deleted.keys())

        self.log(f"-- {len(batch)} messages, {len(updated_ids)} updated and {len(deleted_ids)} deleted.")

        if not updated_ids:
            self.log("-- No docs for processing...")
            return True

        self.log("-- Processing...")
        return self.do_process_batch(batch, updated_ids)

    @abc.abstractmethod
    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        pass

    # Helper methods

    # Extract unique Ids using the specified extractors (per topic), if any, or the message ID if not specified.
    @staticmethod
    def extract_ids(batch: list[KeepsMessage], extractors: dict[str, typing.Callable[[KeepsMessage], str]] = {}) -> set[str]:
        ids = set()
        if extractors is None:
            extractors = {}
        for msg in batch:
            id = None
            if msg.size:
                extractor = extractors.get(msg.topic, lambda m: m.id)
                id = extractor(msg)
            if id:
                ids.add(id)
        return ids

    # Convert IDs to string or None
    @staticmethod
    def str_id(id) -> str:
        if not id:
            return None
        if isinstance(id, list):
            return [str(item) for item in id]
        return str(id)

    # Convert datetimes to string or None
    @staticmethod
    def str_date(dt: datetime.datetime):
        return dt.isoformat() if dt else None

    # Convert datetimes to string or 'NULL'
    @staticmethod
    def str_db_date(dt: datetime.datetime):
        return f"'{dt.isoformat()}'" if dt else "NULL"

    # Logs messages with loguru
    def log(self, msg, error=False):
        """Log a message using loguru."""
        if error:
            self.logger.error(msg)
        else:
            self.logger.info(msg)

    @staticmethod
    def remove_nones(values: list | set):
        clean_list = []
        for value in values:
            if value in [None, ""]:
                continue
            clean_list.append(value)
        return clean_list


class LogProcessor(AbstractProcessor):
    def __init__(self, topics: list[str] = [], logMessages: bool = True) -> None:
        super().__init__()
        self.topics = topics
        self.logMessages = logMessages

    def get_kafka_main_topic(self) -> str:
        return self.topics[0] if self.topics else None

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return dict.fromkeys(self.topics[1:]) if self.topics else None

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: list[str]) -> bool:
        self.log("-----------------------")
        self.log(f"batch size: {len(batch)}")

        if self.logMessages:
            for i, msg in enumerate(batch, start=1):
                self.log("")
                self.log(f"message: {i}")
                msg.log()

        self.log("")
        return True


# Export processors
from .user_processor import UserProcessor  # noqa
from .user_relations_processor import UserRelationsProcessor  # noqa
from .course_processor import CourseProcessor  # noqa
from .course_relations_processor import CourseRelationsProcessor  # noqa
from .activity_processor import ActivityProcessor  # noqa
from .enrollment_processor import EnrollmentProcessor  # noqa
from .enrollment_relations_processor import EnrollmentRelationsProcessor  # noqa
from .course_ratings_processor import CourseRatingsProcessor  # noqa
from .course_evaluations_processor import CourseEvaluationsProcessor  # noqa
from .course_bookmarks_processor import CourseBookmarksProcessor  # noqa
from .answer_processor import AnswerProcessor  # noqa
from .answer_relations_processor import AnswerRelationsProcessor  # noqa
from .channel_processor import ChannelProcessor  # noqa
from .channel_relations_processor import ChannelRelationsProcessor  # noqa
from .channel_subscription_processor import ChannelSubscriptionProcessor  # noqa
from .pulse_processor import PulseProcessor  # noqa
from .pulse_relations_processor import PulseRelationsProcessor  # noqa
from .group_processor import GroupProcessor  # noqa
from .pulse_bookmarks_processor import PulseBookmarksProcessor  # noqa
from .learning_trail_processor import LearningTrailProcessor  # noqa
from .learning_trail_relations_processor import LearningTrailRelationsProcessor  # noqa
from .learning_trail_enrollment_processor import LearningTrailEnrollmentProcessor  # noqa
from .learning_trail_enrollment_relations_processor import LearningTrailEnrollmentRelationsProcessor  # noqa
from .regulatory_compliance.mission_enrollment_processor import MissionEnrollmentProcessor as RegulatoryMissionEnrollmentProcessor  # noqa
from .regulatory_compliance.learning_trail_enrollment_processor import LearningTrailEnrollmentProcessor as RegulatoryTrailEnrollmentProcessor  # noqa
from .regulatory_compliance.mission_processor import MissionProcessor as RegulatoryMissionProcessor  # noqa
from .regulatory_compliance.learning_trail_processor import LearningTrailProcessor as RegulatoryTrailProcessor  # noqa
from .regulatory_compliance.mission_relations_processor import MissionRelationsProcessor as RegulatoryMissionRelationsProcessor  # noqa
from .regulatory_compliance.learning_trail_relations_processor import LearningTrailRelationsProcessor as RegulatoryTrailRelationsProcessor  # noqa

# Smartzap
from .smartzap.service_workspace_processor import ServiceWorkspaceProcessor as SmartzapServiceWorkspaceProcessor  # noqa
from .smartzap.user_workspace_processor import UserWorkspaceProcessor as SmartzapUserWorkspaceProcessor  # noqa
from .smartzap.user_processor import UserProcessor as SmartzapUserProcessor  # noqa

# Konquest
from .konquest.user_role_workspace_processor import UserRoleWorkspaceProcessor as KonquestUserRoleWorkspaceProcessor  # noqa

# ActivityProcessor, AnswerProcessor, AnswerRelationsProcessor
# UserProcessor, UserRelationsProcessor, GroupProcessor
# CourseProcessor, CourseRelationsProcessor, CourseRatingsProcessor, CourseEvaluationsProcessor, CourseBookmarksProcessor
# EnrollmentProcessor, EnrollmentRelationsProcessor
# ChannelProcessor, ChannelRelationsProcessor, PulseProcessor, PulseRelationsProcessor
# LearningTrailProcessor, LearningTrailRelationsProcessor, LearningTrailEnrollmentProcessor, LearningTrailEnrollmentRelationsProcessor
# RegulatoryMissionProcessor, RegulatoryMissionRelationsProcessor, RegulatoryMissionEnrollmentProcessor
# RegulatoryTrailProcessor, RegulatoryTrailRelationsProcessor, RegulatoryTrailEnrollmentProcessor
