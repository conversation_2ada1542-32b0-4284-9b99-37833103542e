import os
import typing
from datetime import datetime

from db import transaction_context

from .. import AbstractProcessor, KeepsMessage


ENV_SUFFIX = os.getenv("ENV_SUFFIX", "dev")


class UserRoleWorkspaceProcessor(AbstractProcessor):
    TOPIC_PREFIX = f"pg_myaccount_{ENV_SUFFIX}.public"
    USER_WORKSPACE = "user_role_workspace"
    KONQUEST_ROLES_ID = [
        "297a88de-c34b-4661-be8a-7090fa9a89e5",
        "45f1fc7f-56f4-4208-a347-ee4d84a8f064",
        "97f4a026-f727-4e23-bdf9-971fec7ce20e",
        "a6d23aea-807e-4374-964e-c725b817742d",
        "c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6",
        "5f19d9b6-dc84-4db3-9074-8f8dfbbe51c8",
    ]

    def __init__(self):
        super().__init__()

    def get_kafka_main_topic(self) -> str:
        return f"{self.TOPIC_PREFIX}.{self.USER_WORKSPACE}"

    def get_kafka_sub_topics(self) -> dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    def get_index_name(self) -> str:
        return ""

    def get_index_mappings(self) -> str:
        return ""

    def pre_process_batch(self, batch: list[KeepsMessage]):
        return super().pre_process_batch([msg for msg in batch if msg.size and msg.entity("role_id") in self.KONQUEST_ROLES_ID])

    def do_process_batch(self, batch: list[KeepsMessage], updated_ids: set[str]) -> bool:
        sanitized_ids = {uid.strip('"') for uid in updated_ids}
        filtered_batch = [msg for msg in batch if msg.entity("id") in sanitized_ids]
        with self.db.myaccount_engine.connect() as myaccount_conn, transaction_context(self.db.konquest_engine) as konquest_conn:
            try:
                for msg in filtered_batch:
                    self._toggle_groups_user_workspace(msg, myaccount_conn, konquest_conn, should_enable=True)
            except Exception as e:
                self.log(f"Error enabling konquest user groups: {e!s}")
                raise
        return True

    def remove_deleted_instances(self, _: set[str], delete_msgs: list[KeepsMessage] = []):
        with self.db.myaccount_engine.connect() as myaccount_conn, transaction_context(self.db.konquest_engine) as konquest_conn:
            try:
                for msg in delete_msgs:
                    self._toggle_groups_user_workspace(msg, myaccount_conn, konquest_conn, should_enable=False)
            except Exception as e:
                self.log(f"Error disabling konquest user groups: {e!s}")
                raise

    ########################################################
    # Private methods
    ########################################################
    def _toggle_groups_user_workspace(self, msg: KeepsMessage, myaccount_conn, konquest_conn, should_enable: bool):
        user_id = msg.entity("user_id")
        workspace_id = msg.entity("workspace_id")

        if not user_id or not workspace_id:
            return

        in_ids = ",".join(f"'{role_id}'" for role_id in self.KONQUEST_ROLES_ID)
        result = self.db.run_sql(myaccount_conn, "myaccount/count_user_role_workspace.sql", user_id=user_id, workspace_id=workspace_id, role_ids=in_ids)

        remaining_roles_count = next((row["count"] for row in result), 0)

        if should_enable and remaining_roles_count == 0:
            return

        if not should_enable and remaining_roles_count > 0:
            return

        deleted = not should_enable
        deleted_date = self.str_db_date(None if should_enable else datetime.now())
        action = "enabled" if should_enable else "disabled"

        self.db.run_sql(konquest_conn, "konquest/toggle_user_group.sql", deleted=deleted, deleted_date=deleted_date, user_id=user_id, workspace_id=workspace_id)

        self.log(f'Groups {action} for user "{user_id}" and workspace "{workspace_id}"')
