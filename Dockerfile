# Use a more specific and lighter base image
FROM python:3.12.1-slim

# Add metadata labels
LABEL description="Kafka Data Indexer Application"

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies, create user, install Python deps, and set permissions
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends --assume-yes \
    build-essential \
    libpq-dev \
    librdkafka-dev && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean && \
    groupadd -r appuser && \
    useradd -r -g appuser -d /app -s /bin/bash appuser

# Set working directory
WORKDIR /app

# Copy and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app*.py ./
COPY data-healthcheck.py ./
COPY indexer/ ./indexer/
COPY config/ ./config/
COPY db/ ./db/
COPY es/ ./es/
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Add healthcheck for worker process (detects any app*.py)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ["pgrep", "-f", "python.*app.*\\.py"]

ENTRYPOINT ["python", "-u", "./app.py"]